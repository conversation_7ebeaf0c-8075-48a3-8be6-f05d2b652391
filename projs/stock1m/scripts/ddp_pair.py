"""
PairDataset DDP 使用示例

适用场景：
- 个股时间序列分析：每个样本是单只股票的一天数据
- 时间序列模型：如 LSTM、Transformer、CNN
- 可以正常 batch，适合大批量训练

启动方式：
torchrun --nproc_per_node=8 projs/stock1m/scripts/ddp_pair.py
"""

import os
import sys
import numpy as np
import torch
from torch.utils.data import DataLoader
from torch.utils.data.distributed import DistributedSampler
from loguru import logger

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from DLlib.data import (
    FieldSpec,
    MinuteCube,
    CubeStandardizer,
    TensorPairDataset,
    setup_dist,
    cleanup_dist,
    to_device_batch,
    is_main_process,
    get_rank,
    get_world_size
)


def main():
    """PairDataset DDP 示例"""
    # 设置分布式环境
    local_rank, device = setup_dist()
    
    # 配置日志
    if is_main_process():
        log_dir = "projs/stock1m/logs"
        os.makedirs(log_dir, exist_ok=True)
        logger.add(f"{log_dir}/pair_ddp.log", rotation="1 day")
        logger.info("=== PairDataset DDP 示例 ===")
        logger.info(f"World Size: {get_world_size()}")
    
    try:
        # === 数据准备 ===
        if is_main_process():
            logger.info("加载股票1分钟数据...")
        
        # 加载数据：[F=8, D=61, T=241, N=6000]
        X = np.load("projs/stock1m/data/OHLCVA_Vwap_cube.npy")
        cube_all = MinuteCube.from_fdtN(X)
        
        if is_main_process():
            logger.info(f"数据形状: F={cube_all.F}, D={cube_all.D}, T={cube_all.T}, N={cube_all.N}")
        
        # 拆分训练/验证集
        cube_train, cube_valid, _, _ = cube_all.split_days(train_ratio=0.8)
        
        # 标准化
        std = CubeStandardizer(mode="per_stock_f").fit(cube_train)
        cube_train_std = std.transform(cube_train)
        cube_valid_std = std.transform(cube_valid)
        
        # 通道规格：7个特征 + 1个标签
        spec = FieldSpec(n_feat=7, n_label=1, n_weight=0)
        
        if is_main_process():
            logger.info(f"数据准备完成 - 训练集: {cube_train_std.D}天, 验证集: {cube_valid_std.D}天")
        
        # === 创建 PairDataset ===
        ds_train = TensorPairDataset(cube_train_std, spec)
        ds_valid = TensorPairDataset(cube_valid_std, spec)
        
        logger.info(f"Rank {get_rank()}: 训练集长度={len(ds_train)}, 验证集长度={len(ds_valid)}")
        
        # === 创建分布式采样器（固定形状样本用官方采样器）===
        train_sampler = DistributedSampler(
            ds_train,
            shuffle=True,
            drop_last=False
        )
        
        valid_sampler = DistributedSampler(
            ds_valid,
            shuffle=False,
            drop_last=False
        )
        
        # === 创建 DataLoader ===
        batch_size = 128  # 可以使用较大的批量大小
        
        train_loader = DataLoader(
            ds_train,
            batch_size=batch_size,
            sampler=train_sampler,
            pin_memory=True,
            num_workers=4,
            persistent_workers=True
        )
        
        valid_loader = DataLoader(
            ds_valid,
            batch_size=batch_size,
            sampler=valid_sampler,
            pin_memory=True,
            num_workers=4,
            persistent_workers=True
        )
        
        logger.info(f"Rank {get_rank()}: DataLoader 创建完成，批量大小: {batch_size}")
        
        # === 数据迭代示例 ===
        logger.info(f"Rank {get_rank()}: 开始数据迭代测试...")
        
        # 模拟训练循环
        for epoch in range(2):
            train_sampler.set_epoch(epoch)  # 重要：每个epoch设置随机种子
            valid_sampler.set_epoch(epoch)
            
            logger.info(f"Rank {get_rank()}: Epoch {epoch} 开始")
            
            # 训练阶段
            for batch_idx, batch in enumerate(train_loader):
                # 传输到 GPU
                batch_gpu = to_device_batch(batch, device)
                
                features = batch_gpu['features']  # [B, T=241, F_feat=7]
                labels = batch_gpu['label']       # [B, T=241, F_label=1]
                weights = batch_gpu['weight']     # [B, T=241, 1] (全1)
                
                if batch_idx == 0:
                    logger.info(f"Rank {get_rank()}: Epoch {epoch}, 批次 {batch_idx}")
                    logger.info(f"  特征形状: {features.shape}")
                    logger.info(f"  标签形状: {labels.shape}")
                    logger.info(f"  权重形状: {weights.shape}")
                    logger.info(f"  设备: {features.device}")
                
                # 这里可以添加模型前向传播
                # pred = model(features)  # 输入 [B, T, F]
                # loss = loss_fn(pred, labels, weights)
                
                if batch_idx >= 5:  # 测试更多批次
                    break
            
            # 验证阶段
            for batch_idx, batch in enumerate(valid_loader):
                batch_gpu = to_device_batch(batch, device)
                
                if batch_idx == 0:
                    logger.info(f"Rank {get_rank()}: Epoch {epoch}, 验证批次 {batch_idx}")
                    logger.info(f"  特征形状: {batch_gpu['features'].shape}")
                
                if batch_idx >= 2:
                    break
        
        if is_main_process():
            logger.info("✅ PairDataset DDP 示例完成！")
            logger.info("适用场景：个股时间序列分析、LSTM/Transformer模型")
    
    except Exception as e:
        logger.error(f"Rank {get_rank()}: 错误 - {e}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        cleanup_dist()


if __name__ == "__main__":
    main()
