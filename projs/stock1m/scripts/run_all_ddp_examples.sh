#!/bin/bash

# 运行所有4种 Dataset 的 DDP 示例

echo "=== 股票1分钟数据 - 4种Dataset DDP示例 ==="

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export NCCL_DEBUG=WARN
export NCCL_IGNORE_DISABLED_P2P=1

# 检查 GPU 数量
GPU_COUNT=$(nvidia-smi --list-gpus | wc -l)
echo "检测到 $GPU_COUNT 个 GPU"

if [ $GPU_COUNT -eq 0 ]; then
    echo "❌ 未检测到 GPU，无法进行 DDP 测试"
    exit 1
fi

# 根据 GPU 数量选择进程数
if [ $GPU_COUNT -ge 8 ]; then
    NPROC=8
elif [ $GPU_COUNT -ge 4 ]; then
    NPROC=4
elif [ $GPU_COUNT -ge 2 ]; then
    NPROC=2
else
    NPROC=1
fi

echo "使用 $NPROC 个进程进行测试"

# 切换到项目根目录
cd "$(dirname "$0")/../../.."
echo "当前目录: $(pwd)"

# 运行4种Dataset示例
echo ""
echo "1. 运行 DaySectionDataset DDP 示例..."
torchrun --nproc_per_node=$NPROC --master_port=29501 projs/stock1m/scripts/ddp_day_section.py

echo ""
echo "2. 运行 PairDataset DDP 示例..."
torchrun --nproc_per_node=$NPROC --master_port=29502 projs/stock1m/scripts/ddp_pair.py

echo ""
echo "3. 运行 MinuteLookbackWithinDayDataset DDP 示例..."
torchrun --nproc_per_node=$NPROC --master_port=29503 projs/stock1m/scripts/ddp_minute_within_day.py

echo ""
echo "4. 运行 MinuteLookbackAcrossDaysDataset DDP 示例..."
torchrun --nproc_per_node=$NPROC --master_port=29504 projs/stock1m/scripts/ddp_minute_across_days.py

echo ""
echo "✅ 所有4种Dataset DDP示例运行完成！"
