"""
DaySectionDataset DDP 使用示例

适用场景：
- 全截面分析：需要同时处理一天内所有有效股票的数据
- 截面模型：如因子模型、排序模型、相对价值模型
- 每天的有效股票数不同（变长样本）

启动方式：
torchrun --nproc_per_node=8 projs/stock1m/scripts/ddp_day_section.py
"""

import os
import sys
import numpy as np
import torch
from torch.utils.data import DataLoader
from loguru import logger

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from DLlib.data import (
    FieldSpec,
    MinuteCube,
    CubeStandardizer,
    TensorDaySectionDataset,
    DistributedSingleIndexBatchSampler,
    passthrough_collate_dict,
    setup_dist,
    cleanup_dist,
    to_device_batch,
    is_main_process,
    get_rank,
    get_world_size
)


def main():
    """DaySectionDataset DDP 示例"""
    # 设置分布式环境
    local_rank, device = setup_dist()
    
    # 配置日志
    if is_main_process():
        log_dir = "projs/stock1m/logs"
        os.makedirs(log_dir, exist_ok=True)
        logger.add(f"{log_dir}/day_section_ddp.log", rotation="1 day")
        logger.info("=== DaySectionDataset DDP 示例 ===")
        logger.info(f"World Size: {get_world_size()}")
    
    try:
        # === 数据准备 ===
        if is_main_process():
            logger.info("加载股票1分钟数据...")
        
        # 加载数据：[F=8, D=61, T=241, N=6000]
        X = np.load("projs/stock1m/data/OHLCVA_Vwap_cube.npy")
        cube_all = MinuteCube.from_fdtN(X)
        
        if is_main_process():
            logger.info(f"数据形状: F={cube_all.F}, D={cube_all.D}, T={cube_all.T}, N={cube_all.N}")
            logger.info(f"每天有效股票数示例: {[len(cube_all.valid_lists[d]) for d in range(min(5, cube_all.D))]}")
        
        # 拆分训练/验证集
        cube_train, cube_valid, _, _ = cube_all.split_days(train_ratio=0.8)
        
        # 标准化
        std = CubeStandardizer(mode="per_stock_f").fit(cube_train)
        cube_train_std = std.transform(cube_train)
        cube_valid_std = std.transform(cube_valid)
        
        # 通道规格：7个特征 + 1个标签
        spec = FieldSpec(n_feat=7, n_label=1, n_weight=0)
        
        if is_main_process():
            logger.info(f"数据准备完成 - 训练集: {cube_train_std.D}天, 验证集: {cube_valid_std.D}天")
        
        # === 创建 DaySectionDataset ===
        ds_train = TensorDaySectionDataset(cube_train_std, spec)
        ds_valid = TensorDaySectionDataset(cube_valid_std, spec)
        
        logger.info(f"Rank {get_rank()}: 训练集长度={len(ds_train)}, 验证集长度={len(ds_valid)}")
        
        # === 创建分布式采样器（变长样本专用）===
        train_sampler = DistributedSingleIndexBatchSampler(
            num_samples=len(ds_train),
            shuffle=True,
            drop_last=False
        )
        
        valid_sampler = DistributedSingleIndexBatchSampler(
            num_samples=len(ds_valid),
            shuffle=False,
            drop_last=False
        )
        
        # === 创建 DataLoader ===
        train_loader = DataLoader(
            ds_train,
            batch_sampler=train_sampler,
            collate_fn=passthrough_collate_dict,
            pin_memory=True,
            num_workers=2,
            persistent_workers=True
        )
        
        valid_loader = DataLoader(
            ds_valid,
            batch_sampler=valid_sampler,
            collate_fn=passthrough_collate_dict,
            pin_memory=True,
            num_workers=2,
            persistent_workers=True
        )
        
        logger.info(f"Rank {get_rank()}: DataLoader 创建完成")
        
        # === 数据迭代示例 ===
        logger.info(f"Rank {get_rank()}: 开始数据迭代测试...")
        
        # 模拟训练循环
        for epoch in range(2):
            train_sampler.set_epoch(epoch)  # 重要：每个epoch设置随机种子
            
            logger.info(f"Rank {get_rank()}: Epoch {epoch} 开始")
            
            # 训练阶段
            for batch_idx, batch in enumerate(train_loader):
                # 传输到 GPU
                batch_gpu = to_device_batch(batch, device)
                
                features = batch_gpu['features']  # [N_valid, T=241, F_feat=7]
                labels = batch_gpu['label']       # [N_valid, T=241, F_label=1]
                weights = batch_gpu['weight']     # [N_valid, T=241, 1] (全1)
                
                if batch_idx == 0:
                    logger.info(f"Rank {get_rank()}: Epoch {epoch}, 批次 {batch_idx}")
                    logger.info(f"  特征形状: {features.shape}")
                    logger.info(f"  标签形状: {labels.shape}")
                    logger.info(f"  权重形状: {weights.shape}")
                    logger.info(f"  设备: {features.device}")
                
                # 这里可以添加模型前向传播
                # pred = model(features)
                # loss = loss_fn(pred, labels, weights)
                
                if batch_idx >= 2:  # 只测试前3个批次
                    break
            
            # 验证阶段
            for batch_idx, batch in enumerate(valid_loader):
                batch_gpu = to_device_batch(batch, device)
                
                if batch_idx == 0:
                    logger.info(f"Rank {get_rank()}: Epoch {epoch}, 验证批次 {batch_idx}")
                    logger.info(f"  特征形状: {batch_gpu['features'].shape}")
                
                if batch_idx >= 1:  # 只测试前2个批次
                    break
        
        if is_main_process():
            logger.info("✅ DaySectionDataset DDP 示例完成！")
            logger.info("适用场景：全截面分析、因子模型、排序模型")
    
    except Exception as e:
        logger.error(f"Rank {get_rank()}: 错误 - {e}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        cleanup_dist()


if __name__ == "__main__":
    main()
