"""
MinuteLookbackWithinDayDataset DDP 使用示例

适用场景：
- 日内分钟级预测：基于过去L分钟预测下一分钟
- 高频交易模型：需要短期历史信息
- 日内模式识别：不跨天，避免隔夜跳空影响

启动方式：
torchrun --nproc_per_node=8 projs/stock1m/scripts/ddp_minute_within_day.py
"""

import os
import sys
import numpy as np
import torch
from torch.utils.data import DataLoader
from loguru import logger

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from DLlib.data import (
    FieldSpec,
    MinuteCube,
    CubeStandardizer,
    TensorMinuteLookbackWithinDayDataset,
    DistributedSingleIndexBatchSampler,
    passthrough_collate_dict,
    setup_dist,
    cleanup_dist,
    to_device_batch,
    is_main_process,
    get_rank,
    get_world_size
)


def main():
    """MinuteLookbackWithinDayDataset DDP 示例"""
    # 设置分布式环境
    local_rank, device = setup_dist()
    
    # 配置日志
    if is_main_process():
        log_dir = "projs/stock1m/logs"
        os.makedirs(log_dir, exist_ok=True)
        logger.add(f"{log_dir}/minute_within_day_ddp.log", rotation="1 day")
        logger.info("=== MinuteLookbackWithinDayDataset DDP 示例 ===")
        logger.info(f"World Size: {get_world_size()}")
    
    try:
        # === 数据准备 ===
        if is_main_process():
            logger.info("加载股票1分钟数据...")
        
        # 加载数据：[F=8, D=61, T=241, N=6000]
        X = np.load("projs/stock1m/data/OHLCVA_Vwap_cube.npy")
        cube_all = MinuteCube.from_fdtN(X)
        
        if is_main_process():
            logger.info(f"数据形状: F={cube_all.F}, D={cube_all.D}, T={cube_all.T}, N={cube_all.N}")
        
        # 拆分训练/验证集
        cube_train, cube_valid, _, _ = cube_all.split_days(train_ratio=0.8)
        
        # 标准化
        std = CubeStandardizer(mode="per_stock_f").fit(cube_train)
        cube_train_std = std.transform(cube_train)
        cube_valid_std = std.transform(cube_valid)
        
        # 通道规格：7个特征 + 1个标签
        spec = FieldSpec(n_feat=7, n_label=1, n_weight=0)
        
        if is_main_process():
            logger.info(f"数据准备完成 - 训练集: {cube_train_std.D}天, 验证集: {cube_valid_std.D}天")
        
        # === 创建 MinuteLookbackWithinDayDataset ===
        L = 30  # 回看30分钟
        
        ds_train = TensorMinuteLookbackWithinDayDataset(
            cube_train_std, 
            spec, 
            L=L, 
            filter_valid=True
        )
        ds_valid = TensorMinuteLookbackWithinDayDataset(
            cube_valid_std, 
            spec, 
            L=L, 
            filter_valid=True
        )
        
        logger.info(f"Rank {get_rank()}: 回看窗口L={L}, 训练集长度={len(ds_train)}, 验证集长度={len(ds_valid)}")
        
        # === 创建分布式采样器（变长样本专用）===
        train_sampler = DistributedSingleIndexBatchSampler(
            num_samples=len(ds_train),
            shuffle=True,
            drop_last=False
        )
        
        valid_sampler = DistributedSingleIndexBatchSampler(
            num_samples=len(ds_valid),
            shuffle=False,
            drop_last=False
        )
        
        # === 创建 DataLoader ===
        train_loader = DataLoader(
            ds_train,
            batch_sampler=train_sampler,
            collate_fn=passthrough_collate_dict,
            pin_memory=True,
            num_workers=2,
            persistent_workers=True
        )
        
        valid_loader = DataLoader(
            ds_valid,
            batch_sampler=valid_sampler,
            collate_fn=passthrough_collate_dict,
            pin_memory=True,
            num_workers=2,
            persistent_workers=True
        )
        
        logger.info(f"Rank {get_rank()}: DataLoader 创建完成")
        
        # === 数据迭代示例 ===
        logger.info(f"Rank {get_rank()}: 开始数据迭代测试...")
        
        # 模拟训练循环
        for epoch in range(2):
            train_sampler.set_epoch(epoch)  # 重要：每个epoch设置随机种子
            
            logger.info(f"Rank {get_rank()}: Epoch {epoch} 开始")
            
            # 训练阶段
            for batch_idx, batch in enumerate(train_loader):
                # 传输到 GPU
                batch_gpu = to_device_batch(batch, device)
                
                features = batch_gpu['features']  # [N_valid, L=30, F_feat=7]
                labels = batch_gpu['label']       # [N_valid, L=30, F_label=1]
                weights = batch_gpu['weight']     # [N_valid, L=30, 1] (全1)
                
                if batch_idx == 0:
                    logger.info(f"Rank {get_rank()}: Epoch {epoch}, 批次 {batch_idx}")
                    logger.info(f"  特征形状: {features.shape}")
                    logger.info(f"  标签形状: {labels.shape}")
                    logger.info(f"  权重形状: {weights.shape}")
                    logger.info(f"  设备: {features.device}")
                    logger.info(f"  回看窗口: {L}分钟")
                
                # 这里可以添加模型前向传播
                # 典型用法：用前L-1个时间步预测第L个时间步
                # input_seq = features[:, :-1, :]   # [N_valid, L-1, F_feat]
                # target = labels[:, -1, :]         # [N_valid, F_label] 最后一个时间步
                # pred = model(input_seq)
                # loss = loss_fn(pred, target)
                
                if batch_idx >= 10:  # 测试更多批次，因为样本数量大
                    break
            
            # 验证阶段
            for batch_idx, batch in enumerate(valid_loader):
                batch_gpu = to_device_batch(batch, device)
                
                if batch_idx == 0:
                    logger.info(f"Rank {get_rank()}: Epoch {epoch}, 验证批次 {batch_idx}")
                    logger.info(f"  特征形状: {batch_gpu['features'].shape}")
                
                if batch_idx >= 3:
                    break
        
        if is_main_process():
            logger.info("✅ MinuteLookbackWithinDayDataset DDP 示例完成！")
            logger.info("适用场景：日内分钟级预测、高频交易模型、日内模式识别")
    
    except Exception as e:
        logger.error(f"Rank {get_rank()}: 错误 - {e}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        cleanup_dist()


if __name__ == "__main__":
    main()
