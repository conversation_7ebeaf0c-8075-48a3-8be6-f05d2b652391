"""
MinuteLookbackAcrossDaysDataset DDP 使用示例

适用场景：
- 跨天连续预测：需要考虑隔夜信息的模型
- 长期依赖建模：回看窗口可以跨越多天
- 全局时间序列：不受交易日边界限制

启动方式：
torchrun --nproc_per_node=8 projs/stock1m/scripts/ddp_minute_across_days.py
"""

import os
import sys
import numpy as np
import torch
from torch.utils.data import DataLoader
from loguru import logger

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from DLlib.data import (
    FieldSpec,
    MinuteCube,
    CubeStandardizer,
    TensorMinuteLookbackAcrossDaysDataset,
    DistributedSingleIndexBatchSampler,
    passthrough_collate_dict,
    setup_dist,
    cleanup_dist,
    to_device_batch,
    is_main_process,
    get_rank,
    get_world_size
)


def main():
    """MinuteLookbackAcrossDaysDataset DDP 示例"""
    # 设置分布式环境
    local_rank, device = setup_dist()
    
    # 配置日志
    if is_main_process():
        log_dir = "projs/stock1m/logs"
        os.makedirs(log_dir, exist_ok=True)
        logger.add(f"{log_dir}/minute_across_days_ddp.log", rotation="1 day")
        logger.info("=== MinuteLookbackAcrossDaysDataset DDP 示例 ===")
        logger.info(f"World Size: {get_world_size()}")
    
    try:
        # === 数据准备 ===
        if is_main_process():
            logger.info("加载股票1分钟数据...")
        
        # 加载数据：[F=8, D=61, T=241, N=6000]
        X = np.load("projs/stock1m/data/OHLCVA_Vwap_cube.npy")
        cube_all = MinuteCube.from_fdtN(X)
        
        if is_main_process():
            logger.info(f"数据形状: F={cube_all.F}, D={cube_all.D}, T={cube_all.T}, N={cube_all.N}")
        
        # 拆分训练/验证集
        cube_train, cube_valid, _, _ = cube_all.split_days(train_ratio=0.8)
        
        # 标准化
        std = CubeStandardizer(mode="per_stock_f").fit(cube_train)
        cube_train_std = std.transform(cube_train)
        cube_valid_std = std.transform(cube_valid)
        
        # 通道规格：7个特征 + 1个标签
        spec = FieldSpec(n_feat=7, n_label=1, n_weight=0)
        
        if is_main_process():
            logger.info(f"数据准备完成 - 训练集: {cube_train_std.D}天, 验证集: {cube_valid_std.D}天")
        
        # === 创建 MinuteLookbackAcrossDaysDataset ===
        L = 480  # 回看480分钟（约2个交易日）
        
        # 测试不同的过滤模式
        filter_modes = ["anchor", "window", "none"]
        
        for filter_mode in filter_modes:
            logger.info(f"Rank {get_rank()}: 测试过滤模式: {filter_mode}")
            
            ds_train = TensorMinuteLookbackAcrossDaysDataset(
                cube_train_std, 
                spec, 
                L=L,
                filter_mode=filter_mode,
                pad_mode="repeat",
                precompute_windows=True
            )
            ds_valid = TensorMinuteLookbackAcrossDaysDataset(
                cube_valid_std, 
                spec, 
                L=L,
                filter_mode=filter_mode,
                pad_mode="repeat",
                precompute_windows=True
            )
            
            logger.info(f"Rank {get_rank()}: 过滤模式={filter_mode}, 回看窗口L={L}, 训练集长度={len(ds_train)}")
            
            # === 创建分布式采样器（变长样本专用）===
            train_sampler = DistributedSingleIndexBatchSampler(
                num_samples=len(ds_train),
                shuffle=True,
                drop_last=False
            )
            
            # === 创建 DataLoader ===
            train_loader = DataLoader(
                ds_train,
                batch_sampler=train_sampler,
                collate_fn=passthrough_collate_dict,
                pin_memory=True,
                num_workers=2,
                persistent_workers=True
            )
            
            logger.info(f"Rank {get_rank()}: DataLoader 创建完成 (过滤模式: {filter_mode})")
            
            # === 数据迭代示例 ===
            train_sampler.set_epoch(0)
            
            for batch_idx, batch in enumerate(train_loader):
                # 传输到 GPU
                batch_gpu = to_device_batch(batch, device)
                
                features = batch_gpu['features']  # [N_sel, L=480, F_feat=7]
                labels = batch_gpu['label']       # [N_sel, L=480, F_label=1]
                weights = batch_gpu['weight']     # [N_sel, L=480, 1] (全1)
                
                if batch_idx == 0:
                    logger.info(f"Rank {get_rank()}: 过滤模式={filter_mode}, 批次 {batch_idx}")
                    logger.info(f"  特征形状: {features.shape}")
                    logger.info(f"  标签形状: {labels.shape}")
                    logger.info(f"  权重形状: {weights.shape}")
                    logger.info(f"  设备: {features.device}")
                    logger.info(f"  跨天回看窗口: {L}分钟")
                    
                    # 不同过滤模式的说明
                    if filter_mode == "anchor":
                        logger.info("  过滤策略: 只保留锚点当天整天有效的股票")
                    elif filter_mode == "window":
                        logger.info("  过滤策略: 保留窗口覆盖的所有天都整天有效的股票")
                    else:
                        logger.info("  过滤策略: 不过滤，返回全量股票")
                
                # 这里可以添加模型前向传播
                # 典型用法：用前L-1个时间步预测第L个时间步
                # input_seq = features[:, :-1, :]   # [N_sel, L-1, F_feat]
                # target = labels[:, -1, :]         # [N_sel, F_label] 最后一个时间步
                # pred = model(input_seq)
                # loss = loss_fn(pred, target)
                
                if batch_idx >= 2:  # 只测试前3个批次
                    break
            
            # 清理资源
            del ds_train, ds_valid, train_loader
            torch.cuda.empty_cache()
        
        # === 测试不同的填充模式 ===
        if is_main_process():
            logger.info("测试不同填充模式...")
        
        pad_modes = ["repeat", "nan"]
        
        for pad_mode in pad_modes:
            logger.info(f"Rank {get_rank()}: 测试填充模式: {pad_mode}")
            
            ds_test = TensorMinuteLookbackAcrossDaysDataset(
                cube_train_std, 
                spec, 
                L=L,
                filter_mode="anchor",
                pad_mode=pad_mode,
                precompute_windows=True
            )
            
            # 测试第一个样本（可能涉及边界填充）
            sample = ds_test[0]
            features = sample['features']
            
            if pad_mode == "repeat":
                logger.info(f"Rank {get_rank()}: 填充模式=repeat, 特征形状: {features.shape}")
                logger.info(f"  无NaN值: {not torch.isnan(features).any()}")
            else:
                logger.info(f"Rank {get_rank()}: 填充模式=nan, 特征形状: {features.shape}")
                logger.info(f"  包含NaN值: {torch.isnan(features).any()}")
            
            del ds_test
        
        if is_main_process():
            logger.info("✅ MinuteLookbackAcrossDaysDataset DDP 示例完成！")
            logger.info("适用场景：跨天连续预测、长期依赖建模、全局时间序列")
    
    except Exception as e:
        logger.error(f"Rank {get_rank()}: 错误 - {e}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        cleanup_dist()


if __name__ == "__main__":
    main()
