%cd ../../

"""
股票1分钟数据 - DaySectionDataset 训练主程序

适用场景：
- 全截面分析：同时处理一天内所有有效股票的数据
- 截面模型：因子模型、排序模型、相对价值模型
- 变长样本：每天有效股票数不同

启动方式：
torchrun --nproc_per_node=8 projs/stock1m/main.py
"""

import os
import sys
import numpy as np
import torch
import torch.distributed as dist
from torch.utils.data import DataLoader
from loguru import logger


from DLlib.data import (
    FieldSpec,
    MinuteCube,
    CubeStandardizer,
    TensorDaySectionDataset,
    DistributedSingleIndexBatchSampler,
    passthrough_collate_dict,
    setup_dist,
    cleanup_dist,
    is_main_process,
    get_world_size
)
from DLlib.train import ModelTrainer, rmse, ic_cs
from DLlib.models import GRUSeq



# 加载数据：[F=8, D=61, T=241, N=6000]
X = np.load("OHLCVA_Vwap_y1_cube.npy")





cube_all = MinuteCube.from_fdtN(X) # End with: 125G, 15.5s




logger.info(f"数据形状: F={cube_all.F}, D={cube_all.D}, T={cube_all.T}, N={cube_all.N}")
logger.info(f"每天有效股票数: {[len(cube_all.valid_lists[d]) for d in range(min(3, cube_all.D))]}")

# 拆分训练/验证集
cube_train, cube_valid, _, _ = cube_all.split_days(train_ratio=0.8) # End with: 233G, 44.8s

# 标准化
std = CubeStandardizer(mode="per_stock_f").fit(cube_train) # Top 350G, End 233G, 7m 30s




cube_train_std = std.transform(cube_train) # Top 341G, End 320G, 2m 12s

cube_valid_std = std.transform(cube_valid) # End 341G, 33.9s

# np save
np.save("/home/<USER>/tslib/projs/stock1m/data/cube_train_std.npy", cube_train_std)
np.save("/home/<USER>/tslib/projs/stock1m/data/cube_valid_std.npy", cube_valid_std)



# 通道规格：7个特征 + 1个标签
spec = FieldSpec(n_feat=7, n_label=1, n_weight=0)

ds_train = TensorDaySectionDataset(cube_train_std, spec)

ds_valid = TensorDaySectionDataset(cube_valid_std, spec)

