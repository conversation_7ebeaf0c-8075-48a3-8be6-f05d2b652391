"""
股票1分钟数据 - DaySectionDataset 训练主程序

适用场景：
- 全截面分析：同时处理一天内所有有效股票的数据
- 截面模型：因子模型、排序模型、相对价值模型
- 变长样本：每天有效股票数不同

启动方式：
torchrun --nproc_per_node=8 projs/stock1m/main.py
"""

import os
import sys
import numpy as np
import torch
import torch.distributed as dist
from torch.utils.data import DataLoader
from loguru import logger

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from DLlib.data import (
    FieldSpec,
    MinuteCube,
    CubeStandardizer,
    TensorDaySectionDataset,
    DistributedSingleIndexBatchSampler,
    passthrough_collate_dict,
    setup_dist,
    cleanup_dist,
    is_main_process,
    get_world_size
)
from DLlib.train import ModelTrainer, rmse, ic_cs
from DLlib.models import GRUSeq


def main():
    """股票1分钟数据 DaySectionDataset 训练主程序"""
    # 设置分布式环境
    _, device = setup_dist()

    # 配置日志 - 修复路径到项目内
    if is_main_process():
        log_dir = "projs/stock1m/logs"
        os.makedirs(log_dir, exist_ok=True)
        logger.add(f"{log_dir}/training.log", rotation="1 day")
        logger.info("=== 股票1分钟数据 DaySectionDataset 训练 ===")
        logger.info(f"World Size: {get_world_size()}")

    # === 数据准备 ===
    if is_main_process():
        logger.info("加载股票1分钟数据...")

    # 加载数据：[F=8, D=61, T=241, N=6000]
    X = np.load("OHLCVA_Vwap_y1_cube.npy")
    cube_all = MinuteCube.from_fdtN(X)

    if is_main_process():
        logger.info(f"数据形状: F={cube_all.F}, D={cube_all.D}, T={cube_all.T}, N={cube_all.N}")
        logger.info(f"每天有效股票数: {[len(cube_all.valid_lists[d]) for d in range(min(3, cube_all.D))]}")

    # 拆分训练/验证集
    cube_train, cube_valid, _, _ = cube_all.split_days(train_ratio=0.8)

    # 标准化
    std = CubeStandardizer(mode="per_stock_f").fit(cube_train)
    cube_train_std = std.transform(cube_train)
    cube_valid_std = std.transform(cube_valid)

    # 通道规格：7个特征 + 1个标签
    spec = FieldSpec(n_feat=7, n_label=1, n_weight=0)

    if is_main_process():
        logger.info(f"数据准备完成 - 训练集: {cube_train_std.D}天, 验证集: {cube_valid_std.D}天")
    
    # === 创建 DaySectionDataset ===
    ds_train = TensorDaySectionDataset(cube_train_std, spec)
    ds_valid = TensorDaySectionDataset(cube_valid_std, spec)

    # === 创建分布式采样器（变长样本专用）===
    train_sampler = DistributedSingleIndexBatchSampler(
        num_samples=len(ds_train),
        shuffle=True,
        drop_last=False
    )

    valid_sampler = DistributedSingleIndexBatchSampler(
        num_samples=len(ds_valid),
        shuffle=False,
        drop_last=False
    )

    # === 创建 DataLoader ===
    train_loader = DataLoader(
        ds_train,
        batch_sampler=train_sampler,
        collate_fn=passthrough_collate_dict,
        pin_memory=True,
        num_workers=8,
        persistent_workers=True
    )

    valid_loader = DataLoader(
        ds_valid,
        batch_sampler=valid_sampler,
        collate_fn=passthrough_collate_dict,
        pin_memory=True,
        num_workers=8,
        persistent_workers=True
    )

    if is_main_process():
        logger.info(f"DataLoader 创建完成 - 训练集: {len(ds_train)}天, 验证集: {len(ds_valid)}天")
    
    # === 创建模型 ===
    model = GRUSeq(
        input_dim=spec.n_feat,  # 7个特征
        hidden_dim=256,
        num_layers=1,
        dropout=0.1,
        output_dim=1
    )

    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-4)
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=20)

    # === 定义损失函数和指标 ===
    # 关注的时间点（分钟索引）
    T_focus = [30, 60, 90, 120, 150, 180, 210]  # 关心的7个截面

    def loss_fn(pred, target, weight):
        return rmse(pred, target, weight)

    metrics = {
        "rmse": lambda p, y, w: rmse(p, y, w),
        "ic": lambda p, y, _: ic_cs(p, y, T_idx=T_focus, mode="spearman_approx"),
    }

    # === 创建训练器 ===
    trainer = ModelTrainer(
        model=model,
        optimizer=optimizer,
        loss_fn=loss_fn,
        metrics=metrics,
        primary_metric="rmse",  # 以RMSE为主要指标
        primary_higher_better=False,  # RMSE越小越好
        device=device,
        use_ddp=True,
        ddp_find_unused_parameters=False,
        grad_clip_norm=1.0,
        grad_accum_steps=1,
        scheduler=scheduler,
        early_stop_patience=10,
    )

    # === 开始训练 ===
    if is_main_process():
        logger.info("开始训练...")

    save_path = "projs/stock1m/checkpoints/best_model.pt"

    trainer.fit(
        train_loader=train_loader,
        valid_loader=valid_loader,
        num_epochs=20,
        save_path=save_path
    )
    
    if is_main_process():
        logger.info("✅ 训练完成！")
            


    # 确保所有进程同步后再清理
    if dist.is_initialized():
        dist.barrier()
    cleanup_dist()



if __name__ == "__main__":
    main()
