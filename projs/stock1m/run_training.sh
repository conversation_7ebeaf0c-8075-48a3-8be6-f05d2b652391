#!/bin/bash

# 股票1分钟数据 DaySectionDataset 训练启动脚本

echo "=== 股票1分钟数据 DaySectionDataset 训练 ==="

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export NCCL_DEBUG=WARN
export NCCL_IGNORE_DISABLED_P2P=1

# 检查 GPU 数量
GPU_COUNT=$(nvidia-smi --list-gpus | wc -l)
echo "检测到 $GPU_COUNT 个 GPU"

if [ $GPU_COUNT -eq 0 ]; then
    echo "❌ 未检测到 GPU，无法进行训练"
    exit 1
fi

# 根据 GPU 数量选择进程数
if [ $GPU_COUNT -ge 8 ]; then
    NPROC=8
elif [ $GPU_COUNT -ge 4 ]; then
    NPROC=4
elif [ $GPU_COUNT -ge 2 ]; then
    NPROC=2
else
    NPROC=1
fi

echo "使用 $NPROC 个进程进行训练"

# 切换到项目根目录
cd "$(dirname "$0")/../.."
echo "当前目录: $(pwd)"

# 创建日志目录
mkdir -p projs/stock1m/logs

echo "启动训练..."

# 启动训练
torchrun \
    --nproc_per_node=$NPROC \
    --master_port=29500 \
    projs/stock1m/main.py

echo "训练完成"
