"""
Tensor 化数据集类

提供直接输出 PyTorch Tensor 的数据集类，支持标签和权重提取。
统一输出 dict 格式：{"features": Tensor, "weight": Tensor or None, "label": Tensor}
"""

import numpy as np
import torch
from typing import Dict
from torch.utils.data import Dataset

from .cube import MinuteCube
from .field_spec import FieldSpec


class TensorDaySectionDataset(Dataset):
    """
    一天一个样本，输出：
      features: [N_valid, T, F_feat]
      weight:   [N_valid, T, n_weight] 或 None
      label:    [N_valid, T, F_label]
    """
    def __init__(self, cube: MinuteCube, spec: FieldSpec):
        self.cube = cube
        self.spec = spec

    def __len__(self): 
        return self.cube.D

    def __getitem__(self, d: int) -> Dict[str, torch.Tensor]:
        X_TNF = self.cube.day_slice(d)              # [T, N, F_total]
        idx_n = self.cube.valid_lists[d]            # [N_valid]
        x = X_TNF[:, idx_n, :]                      # [T, N_valid, F_total]
        x = np.moveaxis(x, 0, 1)                    # -> [N_valid, T, F_total]
        f_np, w_np, y_np = self.spec.split_np(x)
        f, w, y = self.spec.to_tensor(f_np, w_np, y_np)
        return {"features": f, "weight": w, "label": y}


class TensorPairDataset(Dataset):
    """
    (day, stock) 最小单位，输出可 batch：
      features: [T, F_feat]
      weight:   [T, n_weight] 或 None
      label:    [T, F_label]
    """
    def __init__(self, cube: MinuteCube, spec: FieldSpec):
        self.cube = cube
        self.spec = spec
        dn = cube.valid_dn
        self.d_idx, self.n_idx = np.nonzero(dn)
        self.length = self.d_idx.shape[0]

    def __len__(self): 
        return self.length

    def __getitem__(self, i: int) -> Dict[str, torch.Tensor]:
        d = int(self.d_idx[i])
        n = int(self.n_idx[i])
        x = self.cube.X_dtnf[d, :, n, :]            # [T, F_total]
        f_np, w_np, y_np = self.spec.split_np(x)
        f, w, y = self.spec.to_tensor(f_np, w_np, y_np)
        return {"features": f, "weight": w, "label": y}


class TensorMinuteLookbackWithinDayDataset(Dataset):
    """
    (d,t) 锚点，同日内回看 L 分钟，输出：
      features: [N_valid, L, F_feat], weight 同形，label 同形
    """
    def __init__(self, cube: MinuteCube, spec: FieldSpec, L: int, filter_valid: bool = True):
        self.cube = cube
        self.spec = spec
        self.L = int(L)
        self.D, self.T, self.N, self.F = cube.X_dtnf.shape
        self.filter_valid = filter_valid
        self.win = np.stack([np.clip(np.arange(t - self.L + 1, t + 1, dtype=np.int64), 0, t)
                             for t in range(self.T)], axis=0)  # [T, L]
        self.length = self.D * self.T

    def __len__(self): 
        return self.length

    def __getitem__(self, i: int) -> Dict[str, torch.Tensor]:
        d = i // self.T
        t = i % self.T
        minutes = self.win[t]                         # [L]
        x = self.cube.X_dtnf[d, minutes, :, :]       # [L, N, F_total]
        x = np.moveaxis(x, 0, 1)                     # -> [N, L, F_total]
        if self.filter_valid:
            idx_n = self.cube.valid_lists[d]
            x = x[idx_n, :, :]                       # [N_valid, L, F_total]
        f_np, w_np, y_np = self.spec.split_np(x)
        f, w, y = self.spec.to_tensor(f_np, w_np, y_np)
        return {"features": f, "weight": w, "label": y}


class TensorMinuteLookbackAcrossDaysDataset(Dataset):
    """
    (d,t) 跨天连续分钟回看 L（如需），输出：
      features: [N_sel, L, F_feat], weight/label 同形
    filter_mode: 'anchor' | 'window' | 'none'
    """
    def __init__(self, cube: MinuteCube, spec: FieldSpec, L: int,
                 filter_mode: str = "anchor", pad_mode: str = "repeat", precompute_windows: bool = True):
        self.cube = cube
        self.spec = spec
        self.L = int(L)
        self.filter_mode = filter_mode
        self.pad_mode = pad_mode
        self.D, self.T, self.N, self.F = cube.X_dtnf.shape
        self.G = self.D * self.T
        g = np.arange(self.G, dtype=np.int64)
        self.g2d = g // self.T
        self.g2t = g % self.T

        if precompute_windows:
            base = np.arange(-self.L + 1, 1, dtype=np.int64)
            gw = g[:, None] + base[None, :]
            if pad_mode == "repeat":
                gw = np.clip(gw, 0, None)
            elif pad_mode == "nan":
                self._gw_neg_mask = (gw < 0)
                gw = np.clip(gw, 0, None)
            else:
                raise ValueError(f"pad_mode {pad_mode} not supported")
            self.g_windows = gw
        else:
            self.g_windows = None

    def __len__(self):
        return self.G

    def _window_indices(self, g: int):
        if self.g_windows is not None:
            gw = self.g_windows[g]
            neg_mask = None
            if self.pad_mode == "nan":
                neg_mask = getattr(self, "_gw_neg_mask")[g]
            return gw, neg_mask
        base = np.arange(g - self.L + 1, g + 1, dtype=np.int64)
        if self.pad_mode == "repeat":
            return np.clip(base, 0, None), None
        else:
            neg = base < 0
            return np.clip(base, 0, None), neg

    def __getitem__(self, i: int) -> Dict[str, torch.Tensor]:
        g = int(i)
        d = int(self.g2d[g])
        gw, neg_mask = self._window_indices(g)
        days = (gw // self.T).astype(np.int64)
        minutes = (gw % self.T).astype(np.int64)
        x = self.cube.X_dtnf[days, minutes, :, :]    # [L,N,F_total]
        x = np.moveaxis(x, 0, 1)                     # -> [N,L,F_total]

        if neg_mask is not None and np.any(neg_mask):
            x[:, neg_mask, :] = np.nan

        if self.filter_mode == "anchor":
            idx_n = self.cube.valid_lists[d]
            x = x[idx_n, :, :]
        elif self.filter_mode == "window":
            mask_n = self.cube.valid_dn[days].all(axis=0)
            idx_n = np.nonzero(mask_n)[0]
            x = x[idx_n, :, :]
        # 'none' 就直接用 x

        f_np, w_np, y_np = self.spec.split_np(x)
        f, w, y = self.spec.to_tensor(f_np, w_np, y_np)
        return {"features": f, "weight": w, "label": y}
