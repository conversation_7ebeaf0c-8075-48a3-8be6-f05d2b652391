"""
CubeStandardizer - 数据标准化器

提供全局和按股票的标准化方法，支持处理NaN值和缺失数据。
"""

import numpy as np
import warnings
from typing import Optional
from .cube import MinuteCube


class CubeStandardizer:
    """
    标准化器：只做"拟合/应用标准化"，不做任何填充
    
    mode:
      - 'global_f'   : 每个特征 F 一个 (mu, sd)；沿 (D,T,N) 计算，使用 nanmean/nanstd 忽略占位 NaN。
      - 'per_stock_f': 每个 (N,F) 一个 (mu, sd)；沿 (D,T) 计算，使用 nanmean/nanstd；
                       对"训练期未出现的新股票/占位股票"或 std 太小的条目，回退到全局 (mu_g, sd_g)。

    属性（便于检查/过滤）:
      - stock_seen: [N] 训练期是否见过任意特征的有效值
      - per_stock_seen: [N,F] 训练期该 (n,f) 是否见过有效值
    """
    
    def __init__(self, mode: str = "per_stock_f", eps: float = 1e-6):
        assert mode in ("global_f", "per_stock_f")
        self.mode = mode
        self.eps = eps
        self.mu = None   # [F] or [N,F] (float32)
        self.sd = None   # [F] or [N,F] (float32)
        self.mu_g = None # [F]（全局统计，per_stock_f 回退时用）
        self.sd_g = None # [F]
        self.fitted = False
        self.stock_seen = None       # [N]
        self.per_stock_seen = None   # [N,F]

    def fit(self, cube_train: MinuteCube) -> "CubeStandardizer":
        """在训练数据上拟合标准化参数"""
        X = cube_train.X_dtnf  # [D,T,N,F]
        D, T, N, F = cube_train.D, cube_train.T, cube_train.N, cube_train.F

        # --- 全局统计：沿 (D,T,N) 用 nanmean/nanstd，兜底 (mu=0, sd=1) ---
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", category=RuntimeWarning)
            mu_g = np.nanmean(X, axis=(0,1,2))        # [F]
            sd_g = np.nanstd(X, axis=(0,1,2), ddof=0)
        
        mu_g = np.where(np.isfinite(mu_g), mu_g, 0.0).astype(np.float32)
        sd_g = np.where(np.isfinite(sd_g) & (sd_g >= self.eps), sd_g, 1.0).astype(np.float32)
        self.mu_g, self.sd_g = mu_g, sd_g

        if self.mode == "global_f":
            self.mu, self.sd = mu_g, sd_g
            self.fitted = True
            # 可选：seen 信息
            with warnings.catch_warnings():
                warnings.simplefilter("ignore", category=RuntimeWarning)
                self.per_stock_seen = np.isfinite(X).any(axis=(0,1))  # [N,F]
                self.stock_seen = self.per_stock_seen.any(axis=1)     # [N]
            return self

        # --- per_stock_f：沿 (D,T) 对每个 (N,F) 统计；对未见过/退化条目回退到全局 ---
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", category=RuntimeWarning)
            # 训练期可见性：某 (n,f) 是否出现过有限值
            per_stock_seen = np.isfinite(X).any(axis=(0,1))  # [N,F]
            stock_seen = per_stock_seen.any(axis=1)          # [N]
            mu_ps = np.nanmean(X, axis=(0,1))                # [N,F]
            sd_ps = np.nanstd(X, axis=(0,1), ddof=0)        # [N,F]

        # 无数据或 NaN → 回退到全局；方差过小也回退到全局 sd
        invalid_mu = (~np.isfinite(mu_ps)) | (~per_stock_seen)
        invalid_sd = (~np.isfinite(sd_ps)) | (sd_ps < self.eps) | (~per_stock_seen)
        mu_ps = np.where(invalid_mu, mu_g[None, :], mu_ps)
        sd_ps = np.where(invalid_sd, sd_g[None, :], sd_ps)

        self.mu = mu_ps.astype(np.float32)  # [N,F]
        self.sd = sd_ps.astype(np.float32)  # [N,F]
        self.per_stock_seen = per_stock_seen
        self.stock_seen = stock_seen
        self.fitted = True
        return self

    def transform(
        self,
        cube: MinuteCube,
        out_dtype: np.dtype = np.float32,
        out_memmap_path: Optional[str] = None,
    ) -> MinuteCube:
        """应用标准化变换"""
        assert self.fitted, "Call fit() on train cube first."
        D, T, N, F = cube.D, cube.T, cube.N, cube.F
        shape = (D, T, N, F)

        # 申请输出（数组或 memmap）
        if out_memmap_path is None:
            X_out = np.empty(shape, dtype=out_dtype)
        else:
            X_out = np.memmap(out_memmap_path, mode="w+", dtype=out_dtype, shape=shape)

        # 拷贝并做广播标准化（不会改变 NaN，占位 NaN 会保持 NaN，后续由 valid_dn 过滤）
        np.copyto(X_out, cube.X_dtnf, casting="unsafe")

        if self.mode == "global_f":
            # [D,T,N,F] - [1,1,1,F]
            X_out -= self.mu[None, None, None, :]
            X_out /= self.sd[None, None, None, :]
        else:  # per_stock_f
            # [D,T,N,F] - [1,1,N,F]；对于"未见过的新股票/占位股票"，mu/sd 已在 fit 阶段回退为全局统计
            X_out -= self.mu[None, None, :, :]
            X_out /= self.sd[None, None, :, :]

        # 返回新的 MinuteCube（构造器需要 [F,D,T,N]）
        return MinuteCube(np.moveaxis(X_out, (3,0,1,2), (0,1,2,3)))
