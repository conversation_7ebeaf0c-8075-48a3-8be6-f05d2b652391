"""
DLlib.data - 数据处理模块

提供时间序列数据的加载、预处理和数据集类，支持分布式训练。
"""

from .field_spec import FieldSpec
from .cube import MinuteCube
from .standardizer import CubeStandardizer
from .datasets import (
    TensorDaySectionDataset,
    TensorPairDataset,
    TensorMinuteLookbackWithinDayDataset,
    TensorMinuteLookbackAcrossDaysDataset
)
from .samplers import DistributedSingleIndexBatchSampler
from .collate import passthrough_collate_dict
from .ddp_utils import (
    setup_dist,
    cleanup_dist,
    to_device_batch,
    is_main_process,
    get_world_size,
    get_rank
)

__all__ = [
    # 核心组件
    'FieldSpec',
    'MinuteCube',
    'CubeStandardizer',
    
    # 数据集类
    'TensorDaySectionDataset',
    'TensorPairDataset',
    'TensorMinuteLookbackWithinDayDataset',
    'TensorMinuteLookbackAcrossDaysDataset',
    
    # 采样器和工具
    'DistributedSingleIndexBatchSampler',
    'passthrough_collate_dict',
    
    # DDP 工具
    'setup_dist',
    'cleanup_dist',
    'to_device_batch',
    'is_main_process',
    'get_world_size',
    'get_rank'
]
