"""
训练指标和损失函数

提供RMSE、IC等常用指标，支持DDP分布式训练。
"""

from typing import Optional, Sequence
import torch
import torch.distributed as dist


def ddp_mean_scalar(x: torch.Tensor) -> torch.Tensor:
    """
    DDP环境下对标量进行平均
    
    参数:
        x: 标量张量
    
    返回:
        平均后的标量张量
    """
    if dist.is_available() and dist.is_initialized():
        y = x.detach().clone()
        dist.all_reduce(y, op=dist.ReduceOp.SUM)
        y /= dist.get_world_size()
        return y
    return x


def rmse(pred: torch.Tensor, target: torch.Tensor, weight: Optional[torch.Tensor] = None, eps: float = 1e-12) -> torch.Tensor:
    """
    计算加权RMSE
    
    参数:
        pred: 预测值，形状 [..., C]
        target: 真实值，形状与pred相同
        weight: 权重，可广播到pred，可选
        eps: 数值稳定性参数
    
    返回:
        RMSE值
    """
    err2 = (pred - target) ** 2
    if weight is not None:
        err2 = err2 * weight
        denom = torch.clamp(weight.sum(), min=eps) if weight.numel() == err2.numel() else err2.numel()
        return torch.sqrt(err2.sum() / denom + eps)
    return torch.sqrt(err2.mean() + eps)


def ic_cs(
    pred: torch.Tensor,
    target: torch.Tensor,
    *,
    T_idx: Optional[Sequence[int]] = None,   # 仅用于 [N,T,1] 情况
    mode: str = "pearson",                   # "pearson" | "spearman_approx"
    eps: float = 1e-12,
) -> torch.Tensor:
    """
    计算截面IC (Information Coefficient)
    
    两种输入模式：
      1) 非回看：pred/target ∈ [N, T, 1] -> 对 T_idx 中的每个 t，做 corr_N(pred[:,t,0], target[:,t,0])，最后平均
      2) 回看：  pred/target ∈ [N, 1] 或 [N]  -> 直接 corr_N(pred, target)

    参数:
        pred: 预测值
        target: 真实值
        T_idx: 时间索引序列，仅用于[N,T,1]情况
        mode: 相关系数类型，"pearson"或"spearman_approx"
        eps: 数值稳定性参数
    
    返回:
        IC值
    
    注意:
        - spearman_approx 通过两次 argsort 得到序秩，近似且不可导
        - 若要把 IC 用作 loss，请用 mode="pearson"
        - 不做 NaN 掩码（假设前置清洗已保证）
    """
    assert pred.shape == target.shape, "pred/label 形状不一致"
    x = pred
    y = target

    # squeeze 到 [..., 1] 统一处理
    if x.dim() == 1:
        x = x.unsqueeze(-1)
        y = y.unsqueeze(-1)

    if x.dim() == 3:
        # [N,T,1]：取选定的 T_idx
        N, T, C = x.shape
        assert C == 1, "期望 label 维为 1"
        if T_idx is None:
            T_idx = range(T)
        cors = []
        for t in T_idx:
            xt = x[:, t, 0]
            yt = y[:, t, 0]
            if mode == "spearman_approx":
                xt = xt.argsort().argsort().float()
                yt = yt.argsort().argsort().float()
            xt = xt - xt.mean()
            yt = yt - yt.mean()
            num = (xt * yt).sum()
            den = torch.sqrt(xt.square().sum() * yt.square().sum() + eps)
            cors.append(num / (den + eps))
        ic = torch.stack(cors).mean()
        return ic
    elif x.dim() == 2:
        # [N,1]：直接截面 corr_N
        assert x.shape[1] == 1, "期望 [N,1] 或 [N]"
        xv = x[:, 0]
        yv = y[:, 0]
        if mode == "spearman_approx":
            xv = xv.argsort().argsort().float()
            yv = yv.argsort().argsort().float()
        xv = xv - xv.mean()
        yv = yv - yv.mean()
        num = (xv * yv).sum()
        den = torch.sqrt(xv.square().sum() * yv.square().sum() + eps)
        return num / (den + eps)
    else:
        raise ValueError(f"不支持的形状：{x.shape}")
