"""
测试多卡 DDP 环境下的 GPU 数据传输

使用 torchrun 启动：
torchrun --nproc_per_node=2 examples/test_ddp_gpu.py

或者如果有8卡：
torchrun --nproc_per_node=8 examples/test_ddp_gpu.py
"""

import os
import numpy as np
import torch
import torch.nn as nn
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.utils.data import DataLoader
from torch.utils.data.distributed import DistributedSampler
import sys
from loguru import logger

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from DLlib.data import (
    FieldSpec,
    MinuteCube,
    CubeStandardizer,
    TensorDaySectionDataset,
    TensorPairDataset,
    DistributedSingleIndexBatchSampler,
    passthrough_collate_dict,
    setup_dist,
    cleanup_dist,
    to_device_batch,
    is_main_process,
    get_rank,
    get_world_size
)


class SimpleTimeSeriesModel(nn.Module):
    """简单的时间序列模型用于测试"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 64, output_dim: int = 1):
        super().__init__()
        self.lstm = nn.LSTM(input_dim, hidden_dim, batch_first=True)
        self.fc = nn.Linear(hidden_dim, output_dim)
        
    def forward(self, x):
        # x: [B, T, F] 或 [N_valid, T, F]
        lstm_out, _ = self.lstm(x)  # [B, T, hidden_dim]
        # 取最后一个时间步
        output = self.fc(lstm_out[:, -1, :])  # [B, output_dim]
        return output


def test_gpu_memory_usage(device):
    """测试 GPU 内存使用情况"""
    if device.type == 'cuda':
        torch.cuda.empty_cache()
        memory_allocated = torch.cuda.memory_allocated(device) / 1024**3  # GB
        memory_reserved = torch.cuda.memory_reserved(device) / 1024**3   # GB
        logger.info(f"GPU {device.index} - 已分配: {memory_allocated:.2f}GB, 已保留: {memory_reserved:.2f}GB")
        return memory_allocated, memory_reserved
    return 0, 0


def test_data_transfer_speed(batch, device, num_iterations=10):
    """测试数据传输速度"""
    import time
    
    # 预热
    for _ in range(3):
        batch_gpu = to_device_batch(batch, device)
        torch.cuda.synchronize()
    
    # 测试传输速度
    start_time = time.time()
    for _ in range(num_iterations):
        batch_gpu = to_device_batch(batch, device)
        torch.cuda.synchronize()
    end_time = time.time()
    
    avg_time = (end_time - start_time) / num_iterations * 1000  # ms
    
    # 计算数据大小
    total_size = 0
    for key, tensor in batch.items():
        if tensor is not None:
            total_size += tensor.numel() * tensor.element_size()
    total_size_mb = total_size / 1024**2
    
    logger.info(f"数据传输 - 大小: {total_size_mb:.2f}MB, 平均时间: {avg_time:.2f}ms, 带宽: {total_size_mb/avg_time*1000:.2f}MB/s")
    return avg_time, total_size_mb


def main():
    """主测试函数"""
    # 设置分布式环境
    local_rank, device = setup_dist()
    
    # 配置日志（只在主进程）
    if is_main_process():
        logger.add("ddp_gpu_test.log", rotation="1 day")
        logger.info(f"开始 DDP GPU 测试 - World Size: {get_world_size()}")
    
    logger.info(f"Rank {get_rank()}: 使用设备 {device}")
    
    try:
        # === 检查 GPU 状态 ===
        if device.type == 'cuda':
            gpu_name = torch.cuda.get_device_name(device)
            gpu_memory = torch.cuda.get_device_properties(device).total_memory / 1024**3
            logger.info(f"Rank {get_rank()}: GPU {device.index} - {gpu_name}, 总内存: {gpu_memory:.2f}GB")
        else:
            logger.warning(f"Rank {get_rank()}: 使用 CPU 设备")
        
        # === 数据准备 ===
        if is_main_process():
            logger.info("加载数据...")
        
        X = np.load("projs/stock1m/data/OHLCVA_Vwap_cube.npy")
        cube_all = MinuteCube.from_fdtN(X)
        cube_train, cube_valid, _, _ = cube_all.split_days(train_ratio=0.8)
        
        # 标准化
        std = CubeStandardizer(mode="per_stock_f").fit(cube_train)
        cube_train = std.transform(cube_train)
        cube_valid = std.transform(cube_valid)
        
        # 规范通道定义：7 特征 + 1 标签
        spec = FieldSpec(n_feat=7, n_label=1, n_weight=0)
        
        if is_main_process():
            logger.info(f"数据准备完成 - 训练集: {cube_train.D}天, 验证集: {cube_valid.D}天")
        
        # === 测试变长样本 (DaySection) ===
        logger.info(f"Rank {get_rank()}: 测试 DaySection 数据集...")
        
        ds_day = TensorDaySectionDataset(cube_train, spec)
        sampler_day = DistributedSingleIndexBatchSampler(
            num_samples=len(ds_day),
            shuffle=False  # 测试时不打乱
        )
        
        dl_day = DataLoader(
            ds_day,
            batch_sampler=sampler_day,
            collate_fn=passthrough_collate_dict,
            pin_memory=True,
            num_workers=2,
            persistent_workers=True
        )
        
        # 测试几个批次
        for i, batch in enumerate(dl_day):
            logger.info(f"Rank {get_rank()}: DaySection 批次 {i} - 特征: {batch['features'].shape}")
            
            # 测试内存使用
            test_gpu_memory_usage(device)
            
            # 测试数据传输
            if i == 0:  # 只在第一个批次测试传输速度
                test_data_transfer_speed(batch, device)
            
            # 传输到 GPU
            batch_gpu = to_device_batch(batch, device)
            logger.info(f"Rank {get_rank()}: 数据已传输到 {device}")
            
            # 测试传输后的内存使用
            test_gpu_memory_usage(device)
            
            # 验证数据在正确设备上
            assert batch_gpu['features'].device == device
            assert batch_gpu['label'].device == device
            assert batch_gpu['weight'].device == device
            
            if i >= 2:  # 只测试前3个批次
                break
        
        # === 测试固定形状样本 (Pair) ===
        logger.info(f"Rank {get_rank()}: 测试 Pair 数据集...")
        
        ds_pair = TensorPairDataset(cube_train, spec)
        sampler_pair = DistributedSampler(ds_pair, shuffle=False)
        
        dl_pair = DataLoader(
            ds_pair,
            batch_size=64,
            sampler=sampler_pair,
            pin_memory=True,
            num_workers=2,
            persistent_workers=True
        )
        
        for i, batch in enumerate(dl_pair):
            logger.info(f"Rank {get_rank()}: Pair 批次 {i} - 特征: {batch['features'].shape}")
            
            # 传输到 GPU
            batch_gpu = to_device_batch(batch, device)
            
            # 验证数据在正确设备上
            assert batch_gpu['features'].device == device
            assert batch_gpu['label'].device == device
            assert batch_gpu['weight'].device == device
            
            if i >= 2:
                break
        
        # === 测试模型训练 ===
        logger.info(f"Rank {get_rank()}: 测试模型训练...")
        
        model = SimpleTimeSeriesModel(input_dim=7, hidden_dim=32, output_dim=1)
        model = model.to(device)
        model = DDP(model, device_ids=[local_rank], output_device=local_rank)
        
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
        scaler = torch.amp.GradScaler('cuda')
        
        # 训练几个批次
        model.train()
        for i, batch in enumerate(dl_day):
            if i >= 3:  # 只训练3个批次
                break
                
            batch_gpu = to_device_batch(batch, device)
            x = batch_gpu["features"]  # [N_valid, T, F_feat]
            y = batch_gpu["label"]     # [N_valid, T, F_label]
            
            optimizer.zero_grad(set_to_none=True)
            
            with torch.amp.autocast('cuda', dtype=torch.float16):
                pred = model(x)  # [N_valid, output_dim]
                target = y[:, -1, 0]  # [N_valid] 取最后时间步的第一个标签
                loss = nn.functional.mse_loss(pred.squeeze(), target)
            
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
            
            logger.info(f"Rank {get_rank()}: 训练批次 {i}, Loss: {loss.item():.6f}")
            
            # 测试训练后的内存使用
            test_gpu_memory_usage(device)
        
        if is_main_process():
            logger.info("✅ DDP GPU 测试完成！")
    
    except Exception as e:
        logger.error(f"Rank {get_rank()}: 测试失败 - {e}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        cleanup_dist()


if __name__ == "__main__":
    main()
