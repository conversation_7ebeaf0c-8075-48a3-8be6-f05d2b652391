import warnings
import numpy as np
from warnings import simplefilter
from sklearn.metrics import mean_squared_error
import xgboost as xgb
import pandas as pd
from MLlib.utils.utils import *
import optuna
from optuna.samplers import TPESampler
from xgboost.callback import TrainingCallback

warnings.filterwarnings("ignore")
simplefilter(action="ignore", category=pd.errors.PerformanceWarning)


class GroupedICMetricEarlyStopping(TrainingCallback):
    def __init__(self, dvalid, symbol_dict, n_groups=1, patience=50, verbose=True):
        self.dvalid = dvalid
        self.symbol_dict = symbol_dict
        self.patience = patience
        self.verbose = verbose
        self.best_ic = -1
        self.best_iter = 0
        self.stopped = False
        self.n_groups = n_groups

    def after_iteration(self, model, epoch, evals_log):
        preds = model.predict(self.dvalid)
        labels = self.dvalid.get_label()
        # 计算分组 IC
        current_ic = rank_ic_grouped(self.n_groups, labels, preds, self.symbol_dict)
        if self.verbose:
            print(f"[IC Callback] Iter {epoch}: IC = {current_ic:.6f}")
        # 判断是否 early stop
        if current_ic > self.best_ic:
            self.best_ic = current_ic
            self.best_iter = epoch
        elif epoch - self.best_iter >= self.patience:
            if self.verbose:
                print(
                    f"[IC Callback] Early stopping at iter {epoch}, best IC = {self.best_ic:.6f} at iter {self.best_iter}"
                )
            self.stopped = True
            return True  # True 表示终止训练
        return False


class MyXGB:
    def __init__(self, xgb_params, symbol_col, time_col, use_weight=False) -> None:
        self.xgb_params = xgb_params
        self.symbol_col = symbol_col
        self.time_col = time_col
        self.use_weight = use_weight

    def train(self, X_train: pd.DataFrame, y_train, X_valid: pd.DataFrame, y_valid, stopping_rounds=500):
        symbols = X_valid[self.symbol_col].unique()
        symbol_dict = {sym: np.where(X_valid[self.symbol_col] == sym)[0] for sym in symbols}

        X_train = X_train.drop(columns=[self.symbol_col, self.time_col])
        X_valid = X_valid.drop(columns=[self.symbol_col, self.time_col])

        train_weights = X_train["weight"] if self.use_weight else None
        if "weight" in X_train.columns:
            X_train = X_train.drop(columns="weight")
            X_valid = X_valid.drop(columns="weight")

        dtrain = xgb.DMatrix(X_train, label=y_train, weight=train_weights)
        dvalid = xgb.DMatrix(X_valid, label=y_valid)

        model = xgb.train(
            self.xgb_params,
            dtrain,
            num_boost_round=self.xgb_params.get("n_estimators", 10000),
            evals=[(dvalid, "eval")],
            callbacks=[GroupedICMetricEarlyStopping(dvalid, symbol_dict, patience=stopping_rounds, verbose=True)],
        )
        return model


def optuna_train(
    train,
    valid,
    symbol_column_name,
    date_column_name,
    label_column_name,
    use_weight=False,
    SEED=666,
    n_trials=100,
    metric: str = None,
):
    fixed_params = {
        "objective": "reg:squarederror",
        "eval_metric": "rmse" if metric is None else metric,
        "learning_rate": 0.001,
        "n_estimators": 10000,
        "tree_method": "gpu_hist",
        "seed": SEED,
        "nthread": 50,
        "verbosity": 0,
    }

    trainX, trainY, validX, validY = (
        train.drop(columns=label_column_name),
        train[label_column_name],
        valid.drop(columns=label_column_name),
        valid[label_column_name],
    )

    def objective(trial):
        xgb_params = {
            "lambda": trial.suggest_float("lambda_l2", 1e-8, 100.0),
            "alpha": trial.suggest_float("lambda_l1", 1e-8, 100.0),
            "max_depth": trial.suggest_int("max_depth", 3, 15),
            "subsample": trial.suggest_float("bagging_fraction", 0.1, 1.0),
            "colsample_bytree": trial.suggest_float("feature_fraction", 0.1, 1.0),
            "min_child_weight": trial.suggest_int("min_child_samples", 5, 100),
            "gamma": trial.suggest_float("min_split_gain", 1e-10, 1e-2),
        }
        xgb_params.update(fixed_params)
        print(xgb_params)
        myxgb = MyXGB(xgb_params, symbol_column_name, date_column_name, use_weight=use_weight)
        model = myxgb.train(trainX, trainY, validX, validY)
        if "weight" in validX.columns:
            validX_ = validX.drop(columns="weight")
        else:
            validX_ = validX
        dvalid = xgb.DMatrix(validX_.drop(columns=[symbol_column_name, date_column_name]))
        predictions = model.predict(dvalid)
        score = rank_ic_grouped(1, validY.values, predictions)
        return score

    sampler = TPESampler(seed=SEED)
    study = optuna.create_study(direction="maximize", sampler=sampler)
    study.optimize(objective, n_trials=n_trials)
    best_params = study.best_params
    best_params.update(fixed_params)

    myxgb = MyXGB(best_params, symbol_column_name, date_column_name, use_weight=use_weight)
    model = myxgb.train(trainX, trainY, validX, validY)
    print(f"Best Hyperparameters: {best_params}")
    return model, best_params
