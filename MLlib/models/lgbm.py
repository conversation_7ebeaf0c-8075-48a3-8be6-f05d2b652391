import warnings
import numpy as np
from warnings import simplefilter  # Simplifying warning handling
from sklearn.metrics import mean_squared_error
import lightgbm as lgb
import pandas as pd
from MLlib.utils.utils import *
import optuna
from optuna.samplers import TPESampler

warnings.filterwarnings("ignore")
simplefilter(action="ignore", category=pd.errors.PerformanceWarning)


class MyLGBM:
    def __init__(self, lgb_params, symbol_col, time_col, use_weight=False) -> None:
        self.lgb_params = lgb_params
        self.symbol_col = symbol_col
        self.time_col = time_col
        self.use_weight = use_weight

    def train(self, X_train: pd.DataFrame, y_train, X_valid: pd.DataFrame, y_valid, stopping_rounds=500, period=100):
        symbols = X_valid[self.symbol_col].unique()
        symbol_dict = {sym: np.where(X_valid[self.symbol_col] == sym)[0] for sym in symbols}
        X_train = X_train.drop(columns=[self.symbol_col, self.time_col])
        X_valid = X_valid.drop(columns=[self.symbol_col, self.time_col])
        train_weights = X_train["weight"] if self.use_weight else None
        if "weight" in X_train.columns:
            X_train = X_train.drop(columns="weight")
            X_valid = X_valid.drop(columns="weight")
        lgb_train = lgb.Dataset(X_train, y_train, free_raw_data=False, weight=train_weights)
        lgb_eval = lgb.Dataset(X_valid, y_valid, reference=lgb_train, free_raw_data=False)

        # 自定义 ic loss
        def rolling_ic_metric(preds, train_data):
            rolling_ic_window = 1440 * 7
            # 只能接受两个参数
            labels = train_data.get_label()
            corr_list = []
            for sym in symbol_dict:
                idx = symbol_dict[sym]
                r1 = pd.Series(preds[idx])  # .rank(method="first", pct=True)
                r2 = pd.Series(labels[idx])  # .rank(method="first", pct=True)
                corr_list.append(r1.rolling(rolling_ic_window).corr(r2).mean())
            score = np.nanmean(corr_list)
            if pd.isna(score):
                score = -1
            return "ic", score, True

        def grouped_ic_metric(preds, train_data):
            n_groups = 1
            labels = train_data.get_label()
            score = rank_ic_grouped(n_groups, labels, preds, symbol_dict)
            return "ic", score, True

        if X_valid is None or y_valid is None:
            lgb_model = lgb.train(
                self.lgb_params,
                lgb_train,
                callbacks=[lgb.callback.log_evaluation(period=period)],
            )
        else:
            if "metric" not in self.lgb_params or self.lgb_params["metric"] is None:
                lgb_model = lgb.train(
                    self.lgb_params,
                    lgb_train,
                    feval=[grouped_ic_metric],  # 这里是验证集评估，要用这个的话，参数里metric需要传None
                    valid_sets=lgb_eval,
                    callbacks=[
                        lgb.early_stopping(stopping_rounds=stopping_rounds),
                        lgb.callback.log_evaluation(period=period),
                    ],
                )
            else:
                lgb_model = lgb.train(
                    self.lgb_params,
                    lgb_train,
                    valid_sets=lgb_eval,
                    callbacks=[
                        lgb.early_stopping(stopping_rounds=stopping_rounds),
                        lgb.callback.log_evaluation(period=period),
                    ],
                )
        return lgb_model


def optuna_train(
    train,
    valid,
    symbol_column_name,
    date_column_name,
    label_column_name,
    use_weight=False,
    SEED=666,
    n_trials=100,
    metric: str = None,
):
    fixed_params = {
        "objective": "rmse",
        "metric": metric,
        "boosting_type": "gbdt",
        "learning_rate": 0.001,  # 每个任务自己调，不要optuna
        "n_estimators": 10000,
        "device": "gpu",
        "seed": SEED,
        "n_jobs": 50,
        "verbose": -1,
    }

    trainX, trainY, validX, validY = (
        train.drop(columns=label_column_name),
        train[label_column_name],
        valid.drop(columns=label_column_name),
        valid[label_column_name],
    )

    def objective(trial):
        lgb_params = {
            # "learning_rate": trial.suggest_loguniform("learning_rate", 1e-3, 1),
            "lambda_l1": trial.suggest_float("lambda_l1", 1e-8, 100.0),
            "lambda_l2": trial.suggest_float("lambda_l2", 1e-8, 100.0),
            "num_leaves": trial.suggest_int("num_leaves", 2, 512),
            "max_depth": trial.suggest_int("max_depth", 3, 15),
            "feature_fraction": trial.suggest_float("feature_fraction", 0.1, 1.0),
            "bagging_fraction": trial.suggest_float("bagging_fraction", 0.1, 1.0),
            "bagging_freq": trial.suggest_int("bagging_freq", 1, 10),
            "min_child_samples": trial.suggest_int("min_child_samples", 5, 100),
            "min_split_gain": trial.suggest_float("min_split_gain", 1e-10, 1e-2),
        }
        lgb_params.update(fixed_params)
        print(lgb_params)
        mylgb = MyLGBM(lgb_params, symbol_column_name, date_column_name, use_weight=use_weight)
        lgr = mylgb.train(trainX, trainY, validX, validY)
        if "weight" in validX.columns:
            validX_ = validX.drop(columns="weight")
        else:
            validX_ = validX
        predictions = lgr.predict(validX_.drop(columns=[symbol_column_name, date_column_name]))
        score = rank_ic_grouped(1, validY.values, predictions)
        # score = mean_squared_error(validY.values, predictions)
        return score

    sampler = TPESampler(seed=SEED)
    study = optuna.create_study(direction="maximize", sampler=sampler)
    study.optimize(objective, n_trials=n_trials)
    best_params = study.best_params
    best_params.update(fixed_params)

    mylgb = MyLGBM(best_params, symbol_column_name, date_column_name, use_weight=use_weight)
    lgr = mylgb.train(trainX, trainY, validX, validY)
    print(f"Best Hyperparameters: {best_params}")
    return lgr, best_params
