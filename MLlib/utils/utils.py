"""
MLlib工具函数 - 用于机器学习模型的辅助功能
"""

import numpy as np
from typing import Dict, Optional, Union, Tuple


def rank_ic_grouped(
    n_groups: int, 
    labels: np.ndarray, 
    predictions: np.ndarray, 
    symbol_dict: Optional[Dict] = None
) -> float:
    """
    计算分组信息系数(IC) - 用于评估预测质量
    
    Args:
        n_groups: 分组数量（1表示不分组）
        labels: 真实标签 [N]
        predictions: 预测值 [N] 
        symbol_dict: 股票符号到索引的映射字典 {symbol: indices}
        
    Returns:
        float: IC值，范围[-1, 1]，越高越好
    """
    if symbol_dict is None:
        # 如果没有分组信息，直接计算整体IC
        valid_mask = np.isfinite(labels) & np.isfinite(predictions)
        if np.sum(valid_mask) < 2:
            return -1.0
            
        labels_valid = labels[valid_mask]
        preds_valid = predictions[valid_mask]
        
        # 计算spearman相关性（基于排序）
        return spearman_correlation(labels_valid, preds_valid)
    
    # 按股票分组计算IC
    ic_list = []
    for symbol, indices in symbol_dict.items():
        if len(indices) < 2:
            continue
            
        symbol_labels = labels[indices]
        symbol_preds = predictions[indices]
        
        valid_mask = np.isfinite(symbol_labels) & np.isfinite(symbol_preds)
        if np.sum(valid_mask) < 2:
            continue
            
        symbol_labels_valid = symbol_labels[valid_mask]  
        symbol_preds_valid = symbol_preds[valid_mask]
        
        ic = spearman_correlation(symbol_labels_valid, symbol_preds_valid)
        if not np.isnan(ic):
            ic_list.append(ic)
    
    if len(ic_list) == 0:
        return -1.0
        
    return np.mean(ic_list)


def spearman_correlation(x: np.ndarray, y: np.ndarray) -> float:
    """
    计算Spearman等级相关系数
    
    Args:
        x: 数组1 [N]
        y: 数组2 [N]
        
    Returns:
        float: Spearman相关系数
    """
    if len(x) != len(y) or len(x) < 2:
        return 0.0
        
    # 计算排名
    rank_x = scipy_rankdata(x)
    rank_y = scipy_rankdata(y)
    
    # 计算Pearson相关系数于排名
    return pearson_correlation(rank_x, rank_y)


def scipy_rankdata(a: np.ndarray) -> np.ndarray:
    """
    简化版的rankdata实现（类似scipy.stats.rankdata）
    """
    sorter = np.argsort(a)
    a_sorted = a[sorter]
    
    # 处理相同值的情况（平均排名）
    ranks = np.empty_like(sorter, dtype=float)
    ranks[sorter] = np.arange(1, len(a) + 1)
    
    # 处理tie（相同值）
    if len(a) > 1:
        diff = np.diff(a_sorted)
        tie_positions = np.where(diff == 0)[0]
        
        for tie_pos in tie_positions:
            # 找到所有相同的值
            val = a_sorted[tie_pos]
            mask = (a_sorted == val)
            indices = np.where(mask)[0]
            avg_rank = np.mean(ranks[sorter[indices]])
            ranks[sorter[indices]] = avg_rank
    
    return ranks


def pearson_correlation(x: np.ndarray, y: np.ndarray) -> float:
    """
    计算Pearson相关系数
    """
    if len(x) != len(y) or len(x) < 2:
        return 0.0
        
    mean_x = np.mean(x)
    mean_y = np.mean(y)
    
    numerator = np.sum((x - mean_x) * (y - mean_y))
    denominator = np.sqrt(np.sum((x - mean_x)**2) * np.sum((y - mean_y)**2))
    
    if denominator == 0:
        return 0.0
        
    return numerator / denominator


def extract_valid_samples_from_cube(
    cube,
    include_features: Optional[list] = None,
    label_idx: int = -1
) -> Tuple[np.ndarray, np.ndarray, Dict]:
    """
    从MinuteCube中提取有效的训练样本
    
    Args:
        cube: MinuteCube对象
        include_features: 要包含的特征索引列表，None表示除label外的所有特征
        label_idx: 标签在特征维度中的索引，-1表示最后一个特征
        
    Returns:
        X: 特征矩阵 [N_valid, F_features]
        y: 标签向量 [N_valid]
        metadata: 元数据字典，包含股票和时间信息
    """
    D, T, N, F = cube.X_dtnf.shape
    
    # 确定特征和标签的索引
    if label_idx == -1:
        label_idx = F - 1
        
    if include_features is None:
        feature_indices = list(range(F))
        feature_indices.remove(label_idx)
    else:
        feature_indices = include_features
    
    # 收集所有有效样本
    valid_samples = []
    sample_metadata = {
        'day_ids': [],
        'time_ids': [], 
        'stock_ids': [],
        'symbol_dict': {}
    }
    
    symbol_counter = 0
    sample_idx = 0
    
    for d in range(D):
        for t in range(T):
            # 获取这个时刻的有效股票
            valid_stocks = cube.valid_lists[d] if hasattr(cube, 'valid_lists') else np.arange(N)
            minute_data = cube.X_dtnf[d, t, valid_stocks, :]  # [N_valid, F]
            
            # 检查这个时刻的数据是否有效（非NaN）
            valid_mask = np.isfinite(minute_data).all(axis=1)
            final_valid_stocks = valid_stocks[valid_mask]
            final_valid_data = minute_data[valid_mask]
            
            if len(final_valid_data) == 0:
                continue
                
            # 收集样本
            valid_samples.append(final_valid_data)
            
            # 收集元数据
            n_samples_this_minute = len(final_valid_data)
            sample_metadata['day_ids'].extend([d] * n_samples_this_minute)
            sample_metadata['time_ids'].extend([t] * n_samples_this_minute)
            sample_metadata['stock_ids'].extend(final_valid_stocks.tolist())
            
            # 为IC计算构建symbol_dict
            for stock_id in final_valid_stocks:
                symbol_key = f"stock_{stock_id}"
                if symbol_key not in sample_metadata['symbol_dict']:
                    sample_metadata['symbol_dict'][symbol_key] = []
                sample_metadata['symbol_dict'][symbol_key].append(sample_idx)
                sample_idx += 1
    
    if len(valid_samples) == 0:
        raise ValueError("No valid samples found in the cube")
    
    # 合并所有样本
    all_samples = np.vstack(valid_samples)  # [N_total_valid, F]
    
    # 分离特征和标签
    X = all_samples[:, feature_indices]  # [N_total_valid, F_features]
    y = all_samples[:, label_idx]  # [N_total_valid]
    
    # 转换元数据为numpy数组
    for key in ['day_ids', 'time_ids', 'stock_ids']:
        sample_metadata[key] = np.array(sample_metadata[key])
    
    return X, y, sample_metadata


def create_symbol_dict_for_ic(stock_ids: np.ndarray) -> Dict[str, np.ndarray]:
    """
    为IC计算创建股票符号字典
    
    Args:
        stock_ids: 股票ID数组 [N]
        
    Returns:
        Dict: {stock_symbol: [sample_indices]}
    """
    symbol_dict = {}
    for i, stock_id in enumerate(stock_ids):
        symbol_key = f"stock_{stock_id}"
        if symbol_key not in symbol_dict:
            symbol_dict[symbol_key] = []
        symbol_dict[symbol_key].append(i)
    
    # 转换为numpy数组
    for key in symbol_dict:
        symbol_dict[key] = np.array(symbol_dict[key])
    
    return symbol_dict
