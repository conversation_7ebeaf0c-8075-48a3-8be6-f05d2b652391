from sklearn.model_selection import <PERSON><PERSON><PERSON>, TimeSeriesSplit
import numpy as np
from sklearn.model_selection._split import TimeSeriesSplit
from sklearn.utils.validation import _deprecate_positional_args
from sklearn.utils import indexable
from sklearn.utils.validation import _num_samples


class WindowedTestTimeSeriesSplit(TimeSeriesSplit):
    """
    parameters
    ----------
    n_test_folds: int
        number of folds to be used as testing at each iteration.
        by default, 1.
    """
    @_deprecate_positional_args
    def __init__(self, n_splits=5, *, max_train_size=None, n_test_folds=1):
        super().__init__(n_splits, max_train_size=max_train_size)
        self.n_test_folds=n_test_folds

    def split(self, X, y=None, groups=None):
        """Generate indices to split data into training and test set.
        Parameters
        ----------
        X : array-like of shape (n_samples, n_features)
        y : array-like of shape (n_samples,)
            Always ignored, exists for compatibility.
        groups : array-like of shape (n_samples,)
            Always ignored, exists for compatibility.
        Yields
        ------
        train : ndarray
            The training set indices for that split.
        test : ndarray
            The testing set indices for that split.
        """
        X, y, groups = indexable(X, y, groups)
        n_samples = _num_samples(X)
        n_splits = self.n_splits
        n_folds = n_splits + self.n_test_folds
        if n_folds > n_samples:
            raise ValueError(("Cannot have number of folds ={0} greater"
                 " than the number of samples: {1}.").format(n_folds, n_samples))
        indices = np.arange(n_samples)
        fold_size = (n_samples // n_folds)
        test_size = fold_size * self.n_test_folds # test window
        test_starts = range(fold_size + n_samples % n_folds,
                            n_samples-test_size+1, fold_size) # splits based on fold_size instead of test_size
        for test_start in test_starts:
            if self.max_train_size and self.max_train_size < test_start:
                yield (indices[test_start - self.max_train_size:test_start],
                       indices[test_start:test_start + test_size])
            else:
                yield (indices[:test_start],
                       indices[test_start:test_start + test_size])

def purged_cv(df_train, df_train_feats, num_folds, gap):
    """
    带有间隔的时序cv方法
    df_train: data with columns date_id and target
    df_train_feats: data with all expanded features. index should be the same with df_train
    num_folds: how many folds for CV
    gap: days' gap between sequential trains and valids. data is purged.
    """
    total_dates = len(df_train['date_id'].unique())
    date_ids = df_train['date_id'].values
    fold_size = total_dates // num_folds
    purged_num = gap // 2
    for i in range(num_folds):
        start = i*fold_size
        end = start+fold_size
        
        # purged set ranges
        purged_before_start = start-purged_num
        purged_before_end = start+purged_num
        purged_after_start = end-purged_num
        purged_after_end = end+purged_num
        purged_set = ((date_ids >= purged_before_start) & (date_ids <= purged_before_end)) | \
                 ((date_ids >= purged_after_start) & (date_ids <= purged_after_end))
        
        # Define test_indices excluding the purged set
        test_indices = (date_ids >= start) & (date_ids < end) & ~purged_set
        train_indices = ~test_indices & ~purged_set
        
        df_fold_train = df_train_feats[train_indices]
        df_fold_train_target = df_train['target'][train_indices]
        df_fold_valid = df_train_feats[test_indices]
        df_fold_valid_target = df_train['target'][test_indices]
        yield df_fold_train, df_fold_train_target, df_fold_valid, df_fold_valid_target